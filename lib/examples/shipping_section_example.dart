import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../frameworks/vendor_admin/screens/product_management/product_management_model.dart';
import '../frameworks/vendor_admin/screens/product_management/widgets/shipping_section.dart';
import '../models/entities/product.dart';
import '../models/entities/user.dart';

/// Example demonstrating the Shipping Section widget
class ShippingSectionExample extends StatelessWidget {
  const ShippingSectionExample({super.key});

  @override
  Widget build(BuildContext context) {
    // Create a sample user and product for the example
    final user = User(
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      cookie: 'sample_cookie',
    );

    final product = Product(
      id: '1',
      name: 'Sample Product',
      weight: '2.5',
      length: '30',
      width: '20',
      height: '15',
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Shipping Section Example'),
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: ChangeNotifierProvider(
        create: (_) => ProductManagmentModel(user, p: product),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Product Shipping Information',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16.0),
              
              Text(
                'This widget allows you to manage shipping information for your products:',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              const SizedBox(height: 8.0),
              
              const Text('• Weight input for shipping cost calculations'),
              const Text('• Dimensions (Length, Width, Height) for packaging'),
              const Text('• Real-time updates to the product model'),
              const Text('• Validation for numeric inputs only'),
              
              const SizedBox(height: 24.0),

              // The Shipping Section Widget
              const ShippingSection(),

              const SizedBox(height: 24.0),

              // Display current values
              Consumer<ProductManagmentModel>(
                builder: (context, model, child) {
                  return Container(
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(
                        color: Theme.of(context).dividerColor,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Values:',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12.0),
                        
                        _buildValueRow(
                          context,
                          'Weight:',
                          '${model.product?.weight ?? 'Not set'} kg',
                        ),
                        _buildValueRow(
                          context,
                          'Length:',
                          '${model.product?.length ?? 'Not set'} cm',
                        ),
                        _buildValueRow(
                          context,
                          'Width:',
                          '${model.product?.width ?? 'Not set'} cm',
                        ),
                        _buildValueRow(
                          context,
                          'Height:',
                          '${model.product?.height ?? 'Not set'} cm',
                        ),
                        
                        const SizedBox(height: 12.0),
                        
                        if (_hasShippingInfo(model.product)) ...[
                          const Divider(),
                          const SizedBox(height: 8.0),
                          Text(
                            'Calculated Volume: ${_calculateVolume(model.product)} cm³',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ],
                      ],
                    ),
                  );
                },
              ),

              const SizedBox(height: 24.0),

              // Usage instructions
              Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(
                    color: Colors.blue.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.info,
                          color: Colors.blue,
                          size: 20.0,
                        ),
                        const SizedBox(width: 8.0),
                        Text(
                          'Usage Instructions',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12.0),
                    const Text('1. Enter the product weight in kilograms'),
                    const Text('2. Enter dimensions in centimeters'),
                    const Text('3. Values are automatically saved to the product model'),
                    const Text('4. Only numeric values with decimals are allowed'),
                    const Text('5. All fields are optional but recommended for accurate shipping'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildValueRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          SizedBox(
            width: 80.0,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  bool _hasShippingInfo(Product? product) {
    return product?.length?.isNotEmpty == true &&
           product?.width?.isNotEmpty == true &&
           product?.height?.isNotEmpty == true;
  }

  String _calculateVolume(Product? product) {
    if (!_hasShippingInfo(product)) return '0';
    
    try {
      final length = double.parse(product!.length!);
      final width = double.parse(product.width!);
      final height = double.parse(product.height!);
      final volume = length * width * height;
      return volume.toStringAsFixed(2);
    } catch (e) {
      return '0';
    }
  }
}
