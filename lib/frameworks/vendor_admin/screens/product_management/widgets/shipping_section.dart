import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../product_management_model.dart';

class ShippingSection extends StatelessWidget {
  const ShippingSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ProductManagmentModel>(
      builder: (context, model, child) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10.0),
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(8.0),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4.0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section Title
              Row(
                children: [
                  Icon(
                    Icons.local_shipping,
                    color: Theme.of(context).primaryColor,
                    size: 24.0,
                  ),
                  const SizedBox(width: 8.0),
                  Text(
                    'Shipping',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 16.0),

              // Weight Input
              _buildNumberInput(
                context: context,
                label: 'Weight',
                value: model.product?.weight ?? '',
                onChanged: model.updateWeight,
                hint: 'Enter weight (kg)',
                icon: Icons.scale,
              ),

              const SizedBox(height: 16.0),

              // Dimensions Section
              Text(
                'Dimensions',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              const SizedBox(height: 12.0),

              // Dimensions Row
              Row(
                children: [
                  // Length
                  Expanded(
                    child: _buildNumberInput(
                      context: context,
                      label: 'Length',
                      value: model.product?.length ?? '',
                      onChanged: model.updateLength,
                      hint: 'L (cm)',
                      icon: Icons.straighten,
                      isCompact: true,
                    ),
                  ),
                  const SizedBox(width: 12.0),

                  // Width
                  Expanded(
                    child: _buildNumberInput(
                      context: context,
                      label: 'Width',
                      value: model.product?.width ?? '',
                      onChanged: model.updateWidth,
                      hint: 'W (cm)',
                      icon: Icons.width_normal,
                      isCompact: true,
                    ),
                  ),
                  const SizedBox(width: 12.0),

                  // Height
                  Expanded(
                    child: _buildNumberInput(
                      context: context,
                      label: 'Height',
                      value: model.product?.height ?? '',
                      onChanged: model.updateHeight,
                      hint: 'H (cm)',
                      icon: Icons.height,
                      isCompact: true,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12.0),

              // Info Text
              Container(
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(6.0),
                  border: Border.all(
                    color: Theme.of(context).dividerColor,
                    width: 1.0,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 16.0,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withValues(alpha: 0.6),
                    ),
                    const SizedBox(width: 8.0),
                    Expanded(
                      child: Text(
                        'Shipping information helps calculate accurate shipping costs and packaging requirements.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withValues(alpha: 0.6),
                            ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNumberInput({
    required BuildContext context,
    required String label,
    required String value,
    required Function(String) onChanged,
    required String hint,
    required IconData icon,
    bool isCompact = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!isCompact) ...[
          Text(
            label,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 8.0),
        ],
        TextFormField(
          initialValue: value,
          onChanged: onChanged,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
          ],
          decoration: InputDecoration(
            labelText: isCompact ? label : null,
            hintText: hint,
            prefixIcon: Icon(
              icon,
              size: 20.0,
              color: Theme.of(context)
                  .colorScheme
                  .onSurface
                  .withValues(alpha: 0.6),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1.0,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 2.0,
              ),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: isCompact ? 12.0 : 16.0,
              vertical: isCompact ? 12.0 : 16.0,
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
          ),
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }
}
