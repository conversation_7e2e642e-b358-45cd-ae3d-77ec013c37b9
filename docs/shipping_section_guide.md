# Shipping Section Guide

This guide explains how to use the Shipping Section in the product management screen to add weight and dimensions information to products.

## Overview

The Shipping Section allows vendors to add shipping-related information to their products:

1. **Weight** - The weight of the product in kilograms
2. **Dimensions** - The length, width, and height of the product in centimeters

This information is used for:
- Calculating shipping costs
- Determining packaging requirements
- Optimizing shipping logistics
- Providing accurate information to customers

## Implementation Details

### Model Changes

The following fields have been added to the `Product` model:

```dart
// Shipping information
String? weight;
String? length;
String? width;
String? height;
```

These fields are:
- Stored as strings to allow for decimal values
- Optional (can be null or empty)
- Parsed from and serialized to JSON in the product's data

### UI Components

The shipping section consists of:

1. **Weight Input** - A single number input field for the product weight
2. **Dimensions Section** - Three number input fields for length, width, and height

Each input field:
- Accepts only numeric values (with decimal points)
- Has appropriate validation
- Updates the product model in real-time
- Includes clear labels and icons

### Integration

The shipping section is integrated into the product management screen between the stock management section and the attributes section.

## Usage Guide

### Adding Weight Information

1. Navigate to the product management screen
2. Find the "Shipping" section
3. Enter the product weight in the "Weight" field
4. The value should be in kilograms (e.g., "1.5" for 1.5 kg)

### Adding Dimensions

1. In the same "Shipping" section, find the "Dimensions" subsection
2. Enter the product's:
   - Length in the "Length" field
   - Width in the "Width" field
   - Height in the "Height" field
3. All values should be in centimeters (e.g., "20" for 20 cm)

### Best Practices

- **Be Accurate**: Provide precise measurements for accurate shipping calculations
- **Include All Dimensions**: Fill in all three dimension fields for complete information
- **Use Decimal Points When Needed**: For example, "1.5" for 1.5 kg or "10.5" for 10.5 cm
- **Consider Packaging**: Include the dimensions of the packaged product, not just the product itself

## Technical Implementation

### Model Updates

The `Product` model has been updated to include shipping fields:

```dart
// In Product class
String? weight;
String? length;
String? width;
String? height;
```

These fields are included in the `toManagerJson()` method:

```dart
Map<String, dynamic> toManagerJson() {
  // ... existing code ...
  return {
    // ... existing fields ...
    'weight': weight ?? '',
    'length': length ?? '',
    'width': width ?? '',
    'height': height ?? '',
  };
}
```

And parsed in the `fromJson()` method:

```dart
Product.fromJson(Map parsedJson) {
  // ... existing code ...
  
  // Parse shipping information
  weight = parsedJson['weight']?.toString();
  length = parsedJson['length']?.toString();
  width = parsedJson['width']?.toString();
  height = parsedJson['height']?.toString();
}
```

### ViewModel Methods

The `ProductManagmentModel` has been extended with methods to update shipping information:

```dart
void updateWeight(String weight) {
  _product?.weight = weight;
  notifyListeners();
}

void updateLength(String length) {
  _product?.length = length;
  notifyListeners();
}

void updateWidth(String width) {
  _product?.width = width;
  notifyListeners();
}

void updateHeight(String height) {
  _product?.height = height;
  notifyListeners();
}
```

### UI Component

The `ShippingSection` widget provides the UI for entering shipping information:

```dart
class ShippingSection extends StatelessWidget {
  const ShippingSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ProductManagmentModel>(
      builder: (context, model, child) {
        // UI implementation
      },
    );
  }
}
```

## API Integration

When creating or updating a product, the shipping information is included in the API request:

```json
{
  "id": "123",
  "name": "Sample Product",
  "weight": "1.5",
  "length": "20",
  "width": "15",
  "height": "10",
  // Other product fields...
}
```

## Example

```dart
// Example of setting shipping information programmatically
final product = Product();
product.weight = "1.5";
product.length = "20";
product.width = "15";
product.height = "10";

// Example of reading shipping information
final weight = double.tryParse(product.weight ?? "0") ?? 0;
final volume = double.tryParse(product.length ?? "0") ?? 0 *
               double.tryParse(product.width ?? "0") ?? 0 *
               double.tryParse(product.height ?? "0") ?? 0;
```

## Troubleshooting

### Common Issues

1. **Invalid Input**: Only numeric values with optional decimal points are allowed
2. **Missing Values**: All fields are optional, but providing complete information is recommended
3. **Zero Values**: Enter "0" for dimensions that are not applicable, rather than leaving them empty

### Validation

The input fields automatically validate input to ensure only valid numeric values are entered. Invalid characters are filtered out.

## Future Enhancements

Potential future enhancements for the shipping section:

1. **Unit Selection**: Allow switching between metric (cm/kg) and imperial (in/lb) units
2. **Shipping Class**: Add support for shipping classes or categories
3. **Package Type**: Allow selection of standard package types
4. **Shipping Restrictions**: Add options for shipping restrictions or special handling requirements
5. **Volume Calculation**: Automatically calculate and display the product volume

## Conclusion

The Shipping Section provides a simple and effective way to add weight and dimensions information to products. This information is crucial for accurate shipping calculations and logistics planning.

By providing complete and accurate shipping information, vendors can improve the customer experience and optimize their shipping processes.
